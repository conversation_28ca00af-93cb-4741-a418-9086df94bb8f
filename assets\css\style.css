body {
  background: #ffffff;
  font-family: '<PERSON>bu<PERSON>', 'Noto Sans Thai', sans-serif;
  color: #222;
}
.navbar {
  background: linear-gradient(90deg, #2d6a4f 0%, #14532d 100%);
  box-shadow: 0 2px 8px rgba(0,0,0,0.04);
}
.navbar-brand {
  font-weight: bold;
  font-size: 1.7rem;
  color: #ffd700 !important;
  letter-spacing: 2px;
}
.nav-link {
  color: #fff !important;
  font-weight: 500;
  transition: color 0.2s;
}
.nav-link:hover {
  color: #ffd700 !important;
}
.hero-section {
  background: linear-gradient(120deg, #e6f4ea 60%, #ffffff 100%);
  border-radius: 24px;
  box-shadow: 0 4px 24px rgba(0,0,0,0.07);
  padding: 48px 32px;
  margin-bottom: 32px;
}
.hero-section h1 {
  color: #2d6a4f;
  font-weight: 600;
  font-size: 2.8rem;
}
.hero-section .btn-primary {
  background: #2d6a4f;
  border: none;
  font-weight: 600;
  font-size: 1.2rem;
  padding: 12px 32px;
  border-radius: 24px;
  box-shadow: 0 2px 8px rgba(45,106,79,0.12);
  color: #ffd700;
}
.hero-section .btn-primary:hover {
  background: #14532d;
  color: #fffbe7;
}
.card {
  border: none;
  border-radius: 18px;
  box-shadow: 0 2px 12px rgba(0,0,0,0.06);
}
.card-title {
  color: #2d6a4f;
  font-weight: 700;
}
.card-img-top {
  width: 100%;
  aspect-ratio: 4/3;
  object-fit: contain;
  object-position: center;
  background: #f8f8f8;
  max-height: 240px;
}
footer {
  background: #2d6a4f;
  color: #ffd700;
  letter-spacing: 1px;
  border-top-left-radius: 24px;
  border-top-right-radius: 24px;
}

@media (max-width: 576px) {
  .navbar-brand {
    font-size: 1.1rem;
    letter-spacing: 1px;
  }
  .nav-link {
    font-size: 0.95rem;
  }
  .hero-section {
    padding: 24px 8px;
  }
  .hero-section h1 {
    font-size: 1.5rem;
  }
  .hero-section .btn-primary {
    font-size: 1rem;
    padding: 10px 18px;
  }
  .card-title {
    font-size: 1.1rem;
  }
  .lead {
    font-size: 1rem;
  }
  .row.g-4.mb-5 {
    margin-left: 0 !important;
    margin-right: 0 !important;
    padding-left: 0 !important;
    padding-right: 0 !important;
  }
  .col-md-4 {
    flex: 0 0 100%;
    max-width: 100%;
    padding-left: 0 !important;
    padding-right: 0 !important;
  }
  .card {
    width: 100%;
    margin-bottom: 1.5rem;
  }
} 